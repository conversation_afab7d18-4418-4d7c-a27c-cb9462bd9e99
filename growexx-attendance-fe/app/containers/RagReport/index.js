/* eslint-disable prefer-destructuring */
/* eslint-disable indent */
/* eslint-disable no-underscore-dangle */
import React, { PureComponent } from 'react';
import { Helmet } from 'react-helmet';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';
import {
  <PERSON><PERSON>,
  DatePicker,
  PageHeader,
  Select,
  Tabs,
  notification,
  Tooltip,
  Skeleton,
  Space,
} from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEdit, faPlus } from '@fortawesome/free-solid-svg-icons';
import XLSX from 'xlsx';
import { AgGridReact } from 'ag-grid-react';
import moment from 'moment';
import request from 'utils/request';
import { cloneDeep, get } from 'lodash';
import {
  GENERIC_MOMENT_DATE_FORMAT_DISPLAY,
  RAG_COLOR,
  SPRINT_METRICS_HEADERS,
  SPRINT_METRICS_SHEET_HEADERS_TO_API_MAPPING,
  TRACKER_SHEET_HEADERS,
  TRACKER_SHEET_HEADERS_TO_API_MAPPING,
  USER_LIST_LIMIT,
  messages,
} from './constants';
import {
  API_ENDPOINTS,
  GENERIC_MOMENT_DATE_FORMAT,
  SUPER_ADMIN_LIST,
  ROLES,
  ragDropDownColumns,
} from '../constants';
import { StyledRagDiv } from './styled';
import { setDeepLinkURL } from '../../utils/functions';
import CustomEditableModal from './CustomEditableModal';
import Legends from './Legends';
import CustomInput from '../../components/CustomInput/CustomInput'; // No need for a separate validation module

export class RagReport extends PureComponent {
  constructor(props) {
    super(props);
    this.ragTableRef = React.createRef();
    this.sprintMetricsTableRef = React.createRef();
    this.rowIndexToScrollTo = null;
    this.fetchRagDataTimeout = null;
    this.lastFetchTime = 0;
    this.lastFetchParams = null;

    // AbortControllers for different API types to prevent race conditions
    this.currentApiController = null; // For RAG data requests
    this.currentSprintMetricsController = null; // For Sprint Metrics requests
    this.currentProjectListController = null; // For Project List requests
    this.currentUserListController = null; // For User List requests

    // Request IDs to track and ignore stale responses
    this.currentRequestId = 0;
    this.currentSprintMetricsRequestId = 0;
    this.currentProjectListRequestId = 0;
    this.currentUserListRequestId = 0;

    // Bind methods to ensure proper context
    this.getRowSpan = this.getRowSpan.bind(this);
    this.renderEditableColumn = this.renderEditableColumn.bind(this);
    this.memberVarianceCell = this.memberVarianceCell.bind(this);
    this.onProjectSelect = this.onProjectSelect.bind(this);
    this.onUserSelect = this.onUserSelect.bind(this);
    this.dateRangeFilterHandler = this.dateRangeFilterHandler.bind(this);
    this.changeRowData = this.changeRowData.bind(this);
    this.showModal = this.showModal.bind(this);
    this.closeModal = this.closeModal.bind(this);
    this.containsDropdownFields = this.containsDropdownFields.bind(this);
    this.setBtnDisabled = this.setBtnDisabled.bind(this);
    this.renderFreezeRagButton = this.renderFreezeRagButton.bind(this);
    this.isProjectOwner = this.isProjectOwner.bind(this);
    this.getRowClassName = this.getRowClassName.bind(this);
    this.getRowClassNameSprintMetric = this.getRowClassNameSprintMetric.bind(
      this,
    );

    const query = new URLSearchParams(window.location.search);

    const startDate =
      query.get('start') ||
      moment()
        .subtract(3, 'months')
        .startOf('month')
        .format('YYYY-MM-DD');

    const endDate =
      query.get('end') ||
      moment()
        .endOf('month')
        .format('YYYY-MM-DD');

    const handleCheckboxChange = (event, params) => {
      const { selectedIds } = this.state;
      const { _id } = params.data;
      const isChecked = event.target.checked;
      if (isChecked) {
        // Add _id to selectedIds if it's not already present
        if (!selectedIds.includes(_id)) {
          this.setState(prevState => ({
            selectedIds: [...prevState.selectedIds, _id],
          }));
        }
      } else {
        // Removing _id from selectedIds
        this.setState(prevState => ({
          selectedIds: prevState.selectedIds.filter(id => id !== _id),
        }));
      }
    };

    this.state = {
      user: '',
      selectedIds: [],
      rag: {
        columnDefs: [
          {
            headerName: 'Select to Freeze',
            headerCheckboxSelection: true,
            width: 150,
            headerTooltip: 'Select sprint to freeze',
            sortable: false,
            cellRenderer: params => {
              const { _id, freeze } = params.data;

              if (_id) {
                if (freeze === true) {
                  return 'Sprint Freezed';
                }
                return (
                  <input
                    type="checkbox"
                    checked={this.state.selectedIds.includes(_id)}
                    onChange={e => handleCheckboxChange(e, params)}
                    disabled={freeze === true}
                  />
                );
              }
              return null; // Render nothing if _id does not exist
            },
          },

          {
            headerName: 'Project',
            field: 'project',
            width: 150,
            rowSpan: this.getRowSpan,
            sortable: false,
          },
          {
            headerName: 'Delivery Head comments',
            field: 'deliveryHeadComments',
            width: 210,
            sortable: false,
            cellRenderer: params => {
              if (params.value == null) {
                return this.renderEditableColumn(
                  params,
                  'deliveryHeadComments',
                  'Delivery Head comments',
                );
              }

              return (
                <Tooltip title={params.value} placement="bottom" arrow>
                  <span>
                    {this.renderEditableColumn(
                      params,
                      'deliveryHeadComments',
                      'Delivery Head comments',
                    )}
                  </span>
                </Tooltip>
              );
            },
          },
          {
            headerName: 'Sprint Number',
            field: 'sprintNumber',
            width: 250,
            wrapText: false,
            autoHeight: true,
            rowSpan: this.getRowSpan,
            wrapHeaderText: true,
            cellRenderer: params =>
              params.data._id ? (
                <div className="rag-element">
                  <a
                    style={{ color: 'blue' }}
                    href={params.data && params.data.sprintReport}
                    target="_blank"
                  >
                    {params.value}
                  </a>
                  <div>
                    {moment(params.data.sprintStart).format(
                      GENERIC_MOMENT_DATE_FORMAT_DISPLAY,
                    )}{' '}
                    -{' '}
                    {moment(params.data.sprintEnd).format(
                      GENERIC_MOMENT_DATE_FORMAT_DISPLAY,
                    )}
                  </div>
                </div>
              ) : null,
            cellClassRules: {
              'rag-green-outer': params => {
                if (params.data && params.data.overallRag === RAG_COLOR.GREEN) {
                  return true;
                }
                return false;
              },
              'rag-amber-outer': params => {
                if (params.data && params.data.overallRag === RAG_COLOR.AMBER) {
                  return true;
                }
                return false;
              },
              'rag-red-outer': params => {
                if (params.data && params.data.overallRag === RAG_COLOR.RED) {
                  return true;
                }
                return false;
              },
            },
            sortable: false,
          },
          {
            headerName: 'Open/Close Ratio %',
            field: 'openCloseRatio',
            width: 170,
            headerTooltip:
              'open/close ratio = (completed stories count / total planned stories) * 100',
            rowSpan: this.getRowSpan,
            cellClassRules: {
              'rag-green-outer': params => {
                if (params.value >= 75) {
                  return true;
                }
                return false;
              },
              'rag-amber-outer': params => {
                if (params.value < 75 && params.value >= 55) {
                  return true;
                }
                return false;
              },
              'rag-red-outer': params => {
                if (params.value < 55) {
                  return true;
                }
                return false;
              },
            },
            cellRenderer: params => (
              <Tooltip
                title={`Completed Stories Count: ${
                  params.data.completedStoriesCount
                }, Total Planned Stories: ${params.data.totalPlannedStories}`}
              >
                <span className="rag-element">{params.value}</span>
              </Tooltip>
            ),
            sortable: false,
          },
          {
            headerName: 'Effort Variance %',
            field: 'effortVariance',
            headerTooltip:
              '(Total Spent Efforts - Total Planned Efforts) / Total Planned Efforts * 100',
            width: 150,
            rowSpan: this.getRowSpan,
            cellClassRules: {
              'rag-green-outer': params => {
                if (
                  (params.value || params.value === 0) &&
                  params.value <= 10
                ) {
                  return true;
                }
                return false;
              },
              'rag-amber-outer': params => {
                if (params.value && params.value <= 20 && params.value > 10) {
                  return true;
                }
                return false;
              },
              'rag-red-outer': params => {
                if (params.value && params.value > 20) {
                  return true;
                }
                return false;
              },
            },
            cellRenderer: params => (
              <Tooltip
                title={`Total Spent Efforts: ${Number(
                  params.data.totalSpentEfforts,
                ).toFixed(2)}, Total Planned Efforts: ${Number(
                  params.data.totalPlannedEfforts,
                ).toFixed(2)}`}
              >
                <span className="rag-element">{params.value}</span>
              </Tooltip>
            ),
            sortable: false,
          },
          {
            headerName: 'Bugs Reported',
            field: 'bugsReported',
            width: 150,
            headerTooltip: 'Closed Bugs / Total Bugs',
            rowSpan: this.getRowSpan,
            cellClassRules: {
              'rag-green-outer': params => {
                if (params.value) {
                  const resolvedBugs = Number(params.value.split('/')[0]);
                  const totalBugs = Number(params.value.split('/')[1]);

                  if (resolvedBugs <= 3 && totalBugs === resolvedBugs) {
                    return true;
                  }
                  return false;
                }
                return false;
              },
              'rag-amber-outer': params => {
                if (params.value) {
                  const resolvedBugs = Number(params.value.split('/')[0]);
                  const totalBugs = Number(params.value.split('/')[1]);

                  if (
                    resolvedBugs > 3 &&
                    resolvedBugs <= 5 &&
                    totalBugs === resolvedBugs
                  ) {
                    return true;
                  }
                  return false;
                }
                return false;
              },
              'rag-red-outer': params => {
                if (params.value) {
                  const resolvedBugs = Number(params.value.split('/')[0]);
                  const totalBugs = Number(params.value.split('/')[1]);

                  if (resolvedBugs > 5 || totalBugs !== resolvedBugs) {
                    return true;
                  }
                  return false;
                }
                return false;
              },
            },
            cellRenderer: params => (
              <a
                style={{ color: 'blue' }}
                href={params.data.bugLink}
                target="_blank"
              >
                <span className="rag-element">{params.value}</span>
              </a>
            ),
            sortable: false,
          },
          {
            headerName: 'Bugs Without Labels',
            field: 'unlabelledBugsCount',
            wrapHeaderText: true,
            width: 150,
            cellRenderer: params => (
              <a
                style={{ color: 'blue' }}
                href={params.data.unlabelledBugsCountJQL}
                target="_blank"
              >
                <span className="rag-element">{params.value}</span>
              </a>
            ),
            sortable: false,
          },
          {
            headerName: 'Team',
            field: 'team',
            children: [
              {
                headerName: 'Member',
                field: 'member',
                sortable: false,
              },
              {
                headerName: 'Client Escalation (dev)',
                field: 'memberClientEscalation',
                sortable: false,
                cellRenderer: params =>
                  this.renderEditableColumn(
                    params,
                    'memberClientEscalation',
                    'Client Escalations (dev)',
                  ),
              },
              {
                headerName: 'B2D count',
                field: 'b2dCount',
                cellRenderer: params => {
                  if (params.data && params.data.b2dCount) {
                    return (
                      <div>
                        {params.data.solvedBugCount || 0}/{params.data.b2dCount}
                      </div>
                    );
                  }
                  return <div>-</div>;
                },
                sortable: false,
              },
              {
                headerName: 'Member Effort Variance %',
                field: 'effortVarianceIndivisual',
                headerTooltip:
                  '(Total Spent Efforts (Member) - Total Planned Efforts (Member) ) / Total Planned Efforts (Member) * 100',
                width: 150,
                rowSpan: this.getRowSpan,
                cellClassRules: {
                  'rag-amber-outer': params => {
                    if (
                      params.value &&
                      params.value <= 20 &&
                      params.value > 10
                    ) {
                      return true;
                    }
                    return false;
                  },
                  'rag-green-outer': params => {
                    if (
                      (params.value && params.value <= 10) ||
                      !Number.isFinite(params.value) ||
                      params.value === 0
                    ) {
                      return true;
                    }
                    return false;
                  },
                  'rag-red-outer': params => {
                    const value = params.value
                      ? params.value
                      : (
                          ((params.data.spentHours -
                            params.data.plannedEfforts) /
                            (params.data.plannedEfforts || 1)) *
                          100
                        ).toFixed(2);
                    if (value && value > 20) {
                      return true;
                    }
                    return false;
                  },
                },

                cellRenderer: this.memberVarianceCell,
                sortable: false,
              },
            ],
            sortable: false,
          },
          {
            headerName: 'Developers Count',
            field: 'developersCount',
            width: 150,
          },
          {
            headerName: 'Tech Audit',
            field: 'techAudit',
            width: 180,
            cellRenderer: params =>
              this.renderEditableColumn(params, 'techAudit', 'Tech Audit'),
            sortable: false,
          },

          {
            headerName: 'Process Audit',
            field: 'processAudit',
            width: 180,
            cellRenderer: params =>
              this.renderEditableColumn(
                params,
                'processAudit',
                'Process Audit',
              ),
            sortable: false,
          },
          {
            headerName: 'Client Escalations',
            field: 'clientEscalations',
            width: 180,
            sortable: false,
            cellRenderer: params =>
              this.renderEditableColumn(
                params,
                'clientEscalations',
                'Client Escalations',
              ),
          },
          {
            headerName: 'Risks Identified',
            field: 'risksIdentified',
            width: 180,
            sortable: false,
            cellRenderer: params => {
              if (params.value == null) {
                return this.renderEditableColumn(
                  params,
                  'risksIdentified',
                  'Risks Identified',
                );
              }

              return (
                <Tooltip title={params.value} placement="bottom" arrow>
                  <span>
                    {this.renderEditableColumn(
                      params,
                      'risksIdentified',
                      'Risks Identified',
                    )}
                  </span>
                </Tooltip>
              );
            },
          },
          {
            headerName: 'Mitigation Plan',
            field: 'mitigationPlan',
            width: 180,
            sortable: false,
            cellRenderer: params => {
              if (params.value == null) {
                return this.renderEditableColumn(
                  params,
                  'mitigationPlan',
                  'Mitigation Plan',
                );
              }

              return (
                <Tooltip title={params.value} placement="bottom" arrow>
                  <span>
                    {this.renderEditableColumn(
                      params,
                      'mitigationPlan',
                      'Mitigation Plan',
                    )}
                  </span>
                </Tooltip>
              );
            },
          },
          {
            headerName: 'Comments',
            field: 'comments',
            width: 300,
            sortable: false,
            cellRenderer: params => {
              if (params.value == null) {
                return this.renderEditableColumn(
                  params,
                  'comments',
                  'Comments',
                );
              }

              return (
                <Tooltip title={params.value} placement="bottom" arrow>
                  <span>
                    {this.renderEditableColumn(params, 'comments', 'Comments')}
                  </span>
                </Tooltip>
              );
            },
          },
        ],
        rowData: [],
        isLoading: true,
      },
      sprintMetrics: {
        columnDefs: [
          {
            headerName: 'Project',
            field: 'projectName',
            width: 150,
            wrapHeaderText: true,
            sortable: false,
            rowSpan: params =>
              params.data.projectSpan ? params.data.projectSpan : 1,
          },
          {
            headerName: 'Sprint Number',
            field: 'sprintNumber',
            width: 200,
            wrapText: true,
            autoHeight: true,
            sortable: false,
            wrapHeaderText: true,
            cellRenderer: params =>
              params.data.sprintNumber ? (
                <div className="rag-element">
                  {params.value}
                  <div>
                    {moment(params.data.startDate).format(
                      GENERIC_MOMENT_DATE_FORMAT_DISPLAY,
                    )}{' '}
                    -{' '}
                    {moment(params.data.endDate).format(
                      GENERIC_MOMENT_DATE_FORMAT_DISPLAY,
                    )}
                  </div>
                </div>
              ) : null,
            rowSpan: params =>
              params.data.projectSpan ? params.data.projectSpan : 1,
          },
          {
            headerName: 'Repository',
            field: 'repo',
            width: 150,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Resource',
            field: 'resource',
            width: 150,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Total PRs Raised',
            field: 'prCount',
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'No of PRs Rejected',
            field: 'rejectedPrCount',
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Code Smells',
            field: 'codeSmell',
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Vulnerabilities',
            field: 'vulnerabilities',
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Bugs',
            field: 'bugs',
            width: 70,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Code Coverage',
            field: 'coverage',
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Duplication',
            field: 'duplication',
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'No of APIs created/updated',
            field: 'apiCreated',
            headerTooltip: 'Specify the number of APIs created',
            width: 100,
            sortable: false,
            wrapHeaderText: true,
            cellRenderer: params =>
              this.renderEditableColumn(
                params,
                'apiCreated',
                'API created',
                'sprintMetrics',
              ),
          },
          {
            headerName: 'No of APIs available in swagger',
            field: 'availableAPI',
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'PR Efficiency',
            field: 'prEfficiency',
            headerTooltip:
              '((Total PR Raised - Total PR Rejected) / Total PR Raised) * 100',
            width: 100,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'PR Rating',
            field: 'prRating',
            headerTooltip:
              '1 for >10%, 2 for >25 % < = 50%, 3 for >50% and < 80%, 4 for >80% and <=90%, 5 for 100%',
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Sonar Rating',
            field: 'sonarRating',
            headerTooltip: `1 for Bug > 0, vulnerability > 0, code smells > 20, and duplication > 20%
            2 for Bug = 0, vulnerability = 0, code smell > 10, or duplication > 10% 
            3 for Bug = 0, vulnerability = 0, code smell < 10, and duplication < 10%
            4 for Bug = 0, vulnerability = 0, code smell < 5, and duplication <5%
            5 for 0 vulnerability, 0 code smell, and duplication 0%`,
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Swagger Rating',
            field: 'swaggerRating',
            headerTooltip:
              '(No. of APIs available in Swagger/ No. of APIs Created/Updated) * 100 ',
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Coverage Rating',
            field: 'coverageRating',
            headerTooltip: `"Unit Testing
            (Code Coverage %) 
            5 for >= 90%
            4 for >= 80% and <90%
            3 for >= 50% and <80%
            2 for >= 30% and <50%
            1 for >= 1% and <30%
            0 for 0%"`,
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
          {
            headerName: 'Sprint Average Rating',
            field: 'sprintAverage',
            headerTooltip:
              'Average of 4 ratings: PR, Sonar, Coverage and Swagger',
            width: 90,
            wrapHeaderText: true,
            sortable: false,
          },
        ],
        rowData: [],
        isLoading: true,
      },
      tab: query.get('tab') || 'rag',
      dropdownItems: [
        { value: 'jack', label: 'Jack' },
        { value: 'lucy', label: 'Lucy' },
        { value: 'Yiminghe', label: 'yiminghe' },
        { value: 'disabled', label: 'Disabled', disabled: true },
      ],
      projectList: {
        raw: [],
        data: [],
        isLoading: true,
      },
      userList: {
        data: [],
        userLabelMapping: [],
        isLoading: true,
      },
      projectFilter: {
        index: query.get('project') || '',
        id: '',
        pmUser: '',
      },
      userFilter: {
        user: query.get('user') || '',
      },
      dateFilter: {
        startDate,
        endDate,
      },
      ragDownload: {
        isLoading: false,
      },
      modalVars: {
        modalContent: '',
        modalVisible: false,
        modalContentId: '',
        modalField: '',
        modalTitle: '',
        modalIsEdit: false,
      },
      triggerLoading: false,
      isCronRunning: false,
      isSyncJiraDisabled: true,
    };
  }

  setBtnDisabled = (params, field) => {
    let isDisabled = true;
    if (params.data.projectInfo && params.data.projectInfo.length > 0) {
      isDisabled =
        this.state.user._id !== params.data.projectInfo[0].pmUser &&
        this.state.user.role !== ROLES.BU;
    }
    if (SUPER_ADMIN_LIST.includes(this.state.user.email)) {
      isDisabled = false;
    }
    if (field === 'deliveryHeadComments') {
      isDisabled =
        this.state.user.role !== ROLES.BU && this.state.user.role !== ROLES.HR;
    }
    if (field === 'apiCreated') {
      isDisabled =
        this.state.user.role !== ROLES.BU && this.state.user.role !== ROLES.HR;
    }
    if (params.data.freeze) {
      isDisabled = true;
    }

    if (field === 'memberClientEscalation') {
      let doesAnyoneMatched = false;

      if (
        this.state.user.role === ROLES.PM &&
        get(params, 'data.projectInfo[0].pmUser') === this.state.user._id
      ) {
        isDisabled = false;
        doesAnyoneMatched = true;
      }

      if (this.state.user.role === ROLES.BU) {
        isDisabled = false;
        doesAnyoneMatched = true;
      }

      if (get(params, 'data.freeze')) {
        isDisabled = true;
        doesAnyoneMatched = true;
      }

      if (!doesAnyoneMatched) {
        isDisabled = true;
      }
    }
    return isDisabled;
  };

  freezeRag = async () => {
    try {
      await request(API_ENDPOINTS.FREEZE_RAG, {
        method: 'POST',
        body: {
          ragIds: this.state.selectedIds,
        },
      });
      notification.success({
        message: messages.FREEZE_RAG_SUCCESS,
      });
      // ** Clearing previous states and re-fetching the list after freeze
      this.fetchRagData();
      this.fetchSprintMetricsData();
      this.setState({
        selectedIds: [],
      });
    } catch (e) {
      notification.error({
        message: e.message,
      });
    }
  };

  containsDropdownFields = (params, field) =>
    params.data &&
    ragDropDownColumns.includes(field) &&
    (params.data._id ||
      (field === 'memberClientEscalation' && params.data.member));

  componentDidMount() {
    const query = new URLSearchParams(window.location.search);
    const startDate = query.get('start');
    const endDate = query.get('end');
    const projectFromUrl = query.get('project');

    this.setState(
      prevState => ({
        ...prevState,
        user: JSON.parse(localStorage.getItem('userData')).body,
        dateFilter: {
          startDate: startDate || prevState.dateFilter.startDate,
          endDate: endDate || prevState.dateFilter.endDate,
        },
        // Ensure project filter is properly set from URL
        projectFilter: {
          ...prevState.projectFilter,
          index: projectFromUrl || prevState.projectFilter.index,
        },
      }),
      () => {
        // Only fetch user list without data initially if there's a project filter
        // This prevents the premature API call
        this.fetchUserList(false); // Don't fetch data yet
        this.fetchProjectList();
        this.checkCronStatus();

        // Fetch data after a small delay to ensure all initialization is complete
        setTimeout(() => {
          this.fetchRagData();
          this.fetchSprintMetricsData();
        }, 100);
      },
    );
  }

  renderEditableColumn(params, field, title, renderFor = 'rag') {
    if (this.containsDropdownFields(params, field)) {
      const isDisabled = this.setBtnDisabled(params, field);
      return (
        <CustomInput
          param={field}
          data={params.data[field]}
          isDisabled={isDisabled}
          changeRowData={this.changeRowData}
          id={params.data._id || params.data.ragId}
          member={params.data.member}
          renderFor={renderFor}
        />
      );
    }

    if (params.data && params.data[field]) {
      const isDisabled = this.setBtnDisabled(params, field);
      return (
        <Space>
          {params.data[field].length > 15 ? (
            <span
              style={{
                color: `${Number(params.data[field]) > 0 && '#FF0000'}`,
              }}
            >
              {`${params.data[field].substring(0, 12)}...`}
            </span>
          ) : (
            <span
              style={{
                color: `${Number(params.data[field]) > 0 && '#FF0000'}`,
              }}
            >
              {params.data[field]}
            </span>
          )}
          <Button type="link" disabled={isDisabled}>
            <FontAwesomeIcon
              icon={faEdit}
              onClick={() => {
                this.showModal(
                  params.data[field],
                  title,
                  params.data._id,
                  field,
                  true,
                );
              }}
            />
          </Button>

          {field === 'comments' &&
            [ROLES.PM].includes(this.state.user.role) &&
            this.renderFreezeRagButton(params)}
        </Space>
      );
    }

    if (params.data && params.data._id) {
      const isDisabled = this.setBtnDisabled(params, field);
      return (
        <>
          <Button type="link" className="pad-left-0" disabled={isDisabled}>
            <FontAwesomeIcon
              icon={faPlus}
              onClick={() =>
                this.showModal(
                  params.data[field],
                  title,
                  params.data._id,
                  field,
                  params.data.member,
                  true,
                )
              }
            />
          </Button>

          {field === 'comments' &&
            [ROLES.PM].includes(this.state.user.role) &&
            this.renderFreezeRagButton(params)}
        </>
      );
    }
    return null;
  }

  renderFreezeRagButton(params) {
    return (
      this.isProjectOwner(this.state.user, params.data) && (
        <Button
          className="download-btn"
          data-testid="download-btn"
          disabled={Boolean(params.data.freeze) || !params.data.comments}
          onClick={() => this.handleIndividualRagFreeze(params.data)}
        >
          Confirm
        </Button>
      )
    );
  }

  isProjectOwner(user, data) {
    return Boolean(get(user, '_id') === get(data, 'projectInfo[0].pmUser'));
  }

  showModal = (content, title, id, field, isEdit) => {
    this.setState(prev => ({
      ...prev,
      modalVars: {
        modalContent: content,
        modalVisible: true,
        modalContentId: id,
        modalField: field,
        modalTitle: title,
        modalIsEdit: isEdit,
      },
    }));
  };

  closeModal = () => {
    this.setState(prev => ({
      ...prev,
      modalVars: {
        modalContent: '',
        modalVisible: false,
        modalContentId: '',
        modalField: '',
        modalTitle: '',
        modalIsEdit: false,
      },
    }));
  };

  changeRowData(id, param, inputValue) {
    let found = false;
    if (this.ragTableRef.current) {
      this.ragTableRef.current.api.forEachNode(node => {
        if (!found && (node.data.ragId === id || node.data._id === id)) {
          this.rowIndexToScrollTo = node.rowIndex;
          found = true;
        }
      });
    }

    if (this.sprintMetricsTableRef.current) {
      this.sprintMetricsTableRef.current.api.forEachNode(node => {
        if (!found && node.data._id === id) {
          this.rowIndexToScrollTo = node.rowIndex;
          found = true;
        }
      });
    }

    this.setState(
      prevState => ({
        ...prevState,
        rag: {
          ...prevState.rag,
          rowData: prevState.rag.rowData.map(row => {
            if (row._id === id || row.memberId === id) {
              const updatedRow = { ...row };
              updatedRow[param] = inputValue;
              return updatedRow;
            }
            return row;
          }),
        },
        // **updating the sprintMetrics state to persist when any input value changes ********************************
        sprintMetrics: {
          ...prevState.sprintMetrics,
          rowData: prevState.sprintMetrics.rowData.map(row => {
            if (row._id === id || row.memberId === id) {
              const updatedRow = { ...row };
              updatedRow[param] = inputValue;
              return updatedRow;
            }
            return row;
          }),
        },
      }),
      () => {
        // Only refetch data if specific fields that require server sync are updated
        if (
          param === 'memberClientEscalation' ||
          param === 'clientEscalations'
        ) {
          // Add a small delay to prevent rapid successive API calls
          setTimeout(() => {
            this.fetchRagData();
          }, 300);
        }
      },
    );
  }

  sortRag = data => {
    const sortedData = data.sort((a, b) => {
      const dateA = new Date(a.sprintEnd);
      const dateB = new Date(b.sprintEnd);
      const datePartA = new Date(
        dateA.getFullYear(),
        dateA.getMonth(),
        dateA.getDate(),
      );
      const datePartB = new Date(
        dateB.getFullYear(),
        dateB.getMonth(),
        dateB.getDate(),
      );

      if (datePartA < datePartB) return 1;
      if (datePartA > datePartB) return -1;

      return 0;
    });
    return sortedData;
  };

  fetchRagData() {
    // Clear any existing timeout to prevent multiple rapid calls
    if (this.fetchRagDataTimeout) {
      clearTimeout(this.fetchRagDataTimeout);
    }

    // Debounce the actual API call
    this.fetchRagDataTimeout = setTimeout(() => {
      this.performRagDataFetch();
    }, 50);
  }

  performRagDataFetch() {
    const query = new URLSearchParams(window.location.search);

    // Create a unique signature for this request
    const requestSignature = JSON.stringify({
      project: query.get('project') || this.state.projectFilter.index,
      user: query.get('user') || this.state.userFilter.user,
      start: query.get('start') || this.state.dateFilter.startDate,
      end: query.get('end') || this.state.dateFilter.endDate,
    });

    // Cancel any pending API request FIRST - this is the key fix!
    if (this.currentApiController) {
      this.currentApiController.abort();
    }

    // Create new AbortController for this request
    this.currentApiController = new AbortController();

    // Generate unique request ID to ignore stale responses
    this.currentRequestId += 1;
    const requestId = this.currentRequestId;

    // Prevent duplicate calls with same parameters
    if (this.lastFetchParams === requestSignature) {
      return;
    }

    // Update tracking variables
    this.lastFetchTime = Date.now();
    this.lastFetchParams = requestSignature;

    this.setState(prev => ({ ...prev, rag: { ...prev.rag, isLoading: true } }));
    let URL = `${API_ENDPOINTS.RAG_REPORT}?`;

    if (query.get('project')) {
      URL += `project=${query.get('project')}&`;
    }
    if (this.state.userFilter.user && query.get('user')) {
      URL += `user=${this.state.userList.userLabelMapping[query.get('user')]}&`;
    }
    if (query.get('start') && query.get('end')) {
      URL += `start=${moment(query.get('start')).toISOString()}&end=${moment(
        query.get('end'),
      ).toISOString()}`;
    } else if (
      this.state.dateFilter.startDate &&
      this.state.dateFilter.endDate
    ) {
      URL += `start=${moment(
        this.state.dateFilter.startDate,
      ).toISOString()}&end=${moment(
        this.state.dateFilter.endDate,
      ).toISOString()}`;
    }

    // Add timestamp to prevent caching
    URL += `&_t=${Date.now()}`;

    this.fetchData(URL, this.currentApiController.signal)
      .then(data => {
        // CRITICAL: Only process response if it's from the latest request
        if (requestId !== this.currentRequestId) {
          return;
        }

        const sortedData = this.sortRag(data);
        let isSyncJiraDisabled = true;

        // Check if any unfrozen items exist
        Object.values(sortedData).forEach(key => {
          if (key._id && !key.freeze) {
            isSyncJiraDisabled = false;
          }
        });

        this.setState(prev => ({
          ...prev,
          rag: {
            ...prev.rag,
            rowData: sortedData,
            isLoading: false,
          },
          isSyncJiraDisabled,
        }));

        // Clear the controller after successful completion
        this.currentApiController = null;
      })
      .catch(error => {
        // Don't update state if the request was aborted
        if (error.name === 'AbortError') {
          return;
        }

        // Only update state if this is the latest request
        if (requestId === this.currentRequestId) {
          this.setState(prev => ({
            ...prev,
            rag: { ...prev.rag, isLoading: false },
          }));
        }

        // Clear the controller after error
        this.currentApiController = null;
      });
  }

  fetchSprintMetricsData() {
    this.setState(prev => ({
      ...prev,
      sprintMetrics: { ...prev.sprintMetrics, isLoading: true },
    }));

    // Cancel any pending Sprint Metrics API request
    if (this.currentSprintMetricsController) {
      this.currentSprintMetricsController.abort();
    }

    // Create new AbortController for this request
    this.currentSprintMetricsController = new AbortController();

    // Generate unique request ID to ignore stale responses
    this.currentSprintMetricsRequestId += 1;
    const requestId = this.currentSprintMetricsRequestId;

    const query = new URLSearchParams(window.location.search);
    let URL = `${API_ENDPOINTS.SPRINT_METRICS_REPORT}?`;

    if (query.get('project')) {
      URL += `project=${query.get('project')}&`;
    }
    if (this.state.userFilter.user && query.get('user')) {
      URL += `user=${this.state.userList.userLabelMapping[query.get('user')]}&`;
    }
    if (query.get('start') && query.get('end')) {
      URL += `start=${query.get('start')}&end=${query.get('end')}`;
    }

    // Add timestamp to prevent caching
    URL += `&_t=${Date.now()}`;

    this.fetchData(URL, this.currentSprintMetricsController.signal)
      .then(data => {
        // Only process response if it's from the latest request
        if (requestId !== this.currentSprintMetricsRequestId) {
          return;
        }

        this.setState(prev => ({
          ...prev,
          sprintMetrics: {
            ...prev.sprintMetrics,
            rowData: Array.isArray(data) ? data : [],
            isLoading: false,
          },
        }));

        // Clear the controller after successful completion
        this.currentSprintMetricsController = null;
      })
      .catch(error => {
        // Don't update state if the request was aborted
        if (error.name === 'AbortError') {
          return;
        }

        // Only update state if this is the latest request
        if (requestId === this.currentSprintMetricsRequestId) {
          this.setState(prev => ({
            ...prev,
            sprintMetrics: { ...prev.sprintMetrics, isLoading: false },
          }));
        }

        // Clear the controller after error
        this.currentSprintMetricsController = null;
      });
  }

  fetchProjectList() {
    this.setState(prev => ({
      ...prev,
      projectList: {
        ...prev.projectList,
        isLoading: true,
      },
    }));

    // Cancel any pending Project List API request
    if (this.currentProjectListController) {
      this.currentProjectListController.abort();
    }

    // Create new AbortController for this request
    this.currentProjectListController = new AbortController();

    // Generate unique request ID to ignore stale responses
    this.currentProjectListRequestId += 1;
    const requestId = this.currentProjectListRequestId;

    this.fetchData(
      API_ENDPOINTS.GET_ALL_PROJECTS,
      this.currentProjectListController.signal,
    )
      .then(data => {
        // Only process response if it's from the latest request
        if (requestId !== this.currentProjectListRequestId) {
          return;
        }

        this.setState(
          prev => ({
            ...prev,
            projectList: {
              ...prev.projectList,
              raw: Array.isArray(data) ? data : [],
              data: Array.isArray(data)
                ? data.map(project => ({
                    label: project.projectName,
                    value: project.projectName,
                  }))
                : [],
              isLoading: false,
            },
          }),
          () => {
            // Set project ID based on current project filter
            const projectName = this.state.projectFilter.index;
            let projectId = '';
            let pmUser = '';
            if (projectName && Array.isArray(this.state.projectList.raw)) {
              const projectObj = this.state.projectList.raw.find(
                project => project.projectName === projectName,
              );
              if (projectObj) {
                projectId = projectObj._id;
                pmUser = projectObj.pmUser;
              }
            }
            this.setState(prev => ({
              ...prev,
              projectFilter: {
                ...prev.projectFilter,
                id: projectId,
                pmUser,
              },
            }));
          },
        );

        // Clear the controller after successful completion
        this.currentProjectListController = null;
      })
      .catch(error => {
        // Don't update state if the request was aborted
        if (error.name === 'AbortError') {
          return;
        }

        // Only update state if this is the latest request
        if (requestId === this.currentProjectListRequestId) {
          notification.error({
            message: error.message,
          });
        }

        // Clear the controller after error
        this.currentProjectListController = null;
      });
  }

  dateRangeFilterHandler(event) {
    if (!event) {
      this.setState(
        prev => ({
          ...prev,
          dateFilter: {
            ...prev.dateFilter,
            startDate: '',
            endDate: '',
          },
        }),
        () => {
          setDeepLinkURL({
            tab: this.state.tab,
            project: this.state.projectFilter.index,
            user: this.state.userFilter.user,
            start: '',
            end: '',
          });
          // Add small delay to ensure URL is updated before fetching data
          setTimeout(() => {
            this.fetchRagData();
            this.fetchSprintMetricsData();
          }, 100);
        },
      );
    } else {
      const startDate = moment(event[0]).format(GENERIC_MOMENT_DATE_FORMAT);
      const endDate = moment(event[1]).format(GENERIC_MOMENT_DATE_FORMAT);

      this.setState(
        prev => ({
          ...prev,
          dateFilter: {
            ...prev.dateFilter,
            startDate,
            endDate,
          },
        }),
        () => {
          setDeepLinkURL({
            tab: this.state.tab,
            project: this.state.projectFilter.index,
            user: this.state.userFilter.user,
            start: this.state.dateFilter.startDate,
            end: this.state.dateFilter.endDate,
          });
          this.fetchRagData();
          this.fetchSprintMetricsData();
        },
      );
    }
  }

  createTrackerWorkbook = async trackerData => {
    const ws = XLSX.utils.aoa_to_sheet([[]]);
    const header = TRACKER_SHEET_HEADERS;
    XLSX.utils.sheet_add_json(ws, [TRACKER_SHEET_HEADERS_TO_API_MAPPING], {
      header,
      skipHeader: true,
    });

    trackerData.forEach((trackData, index) => {
      const origin = `A${index + 2}`;
      const rowData = { ...trackData };
      if (rowData._id) {
        rowData.sprintNumber += ` [ ${moment(rowData.sprintStart).format(
          GENERIC_MOMENT_DATE_FORMAT_DISPLAY,
        )} - ${moment(rowData.sprintEnd).format(
          GENERIC_MOMENT_DATE_FORMAT_DISPLAY,
        )} ]`;
      }
      delete rowData._id;
      delete rowData.resolvedBugs;
      delete rowData.teamLength;
      delete rowData.totalBugs;
      delete rowData.sprintEnd;
      delete rowData.boardId;
      delete rowData.jiraSprintId;
      delete rowData.boardKey;
      delete rowData.createdAt;
      delete rowData.updatedAt;
      delete rowData.sprintReport;
      delete rowData.bugLink;
      delete rowData.index;
      delete rowData.sprintStart;
      delete rowData.unlabelledBugsCountJQL;
      delete rowData.userId;
      if (rowData.overallRag) {
        rowData.overallRag = rowData.overallRag
          .toLowerCase()
          .replace(
            /(^\w|\s\w)(\w*)/g,
            (_, f, r) => f.toUpperCase() + r.toLowerCase(),
          );
      }
      if (rowData.clientEscalations) {
        rowData.clientEscalations = Number(rowData.clientEscalations);
      }
      if (rowData.techAudit) {
        rowData.techAudit = Number(rowData.techAudit);
      }
      if (rowData.processAudit) {
        rowData.processAudit = Number(rowData.processAudit);
      }
      XLSX.utils.sheet_add_json(ws, [rowData], {
        header,
        origin,
        skipHeader: true,
      });
    });
    return ws;
  };

  createSprintMetricsReport = async trackerData => {
    const ws = XLSX.utils.aoa_to_sheet([[]]);
    const header = SPRINT_METRICS_HEADERS;
    XLSX.utils.sheet_add_json(
      ws,
      [SPRINT_METRICS_SHEET_HEADERS_TO_API_MAPPING],
      {
        header,
        skipHeader: true,
      },
    );
    trackerData.forEach((trackData, index) => {
      const origin = `A${index + 2}`;
      const rowData = { ...trackData };
      if (rowData._id) {
        rowData.sprintNumber += ` [ ${moment(rowData.sprintStart).format(
          GENERIC_MOMENT_DATE_FORMAT_DISPLAY,
        )} - ${moment(rowData.sprintEnd).format(
          GENERIC_MOMENT_DATE_FORMAT_DISPLAY,
        )} ]`;
      }
      delete rowData._id;
      delete rowData.repo;
      delete rowData.startDate;
      delete rowData.endDate;
      delete rowData.createdAt;
      delete rowData.updatedAt;
      delete rowData.projectSpan;
      delete rowData.index;
      delete rowData.userId;
      delete rowData.__v;
      XLSX.utils.sheet_add_json(ws, [rowData], {
        header,
        origin,
        skipHeader: true,
      });
    });
    return ws;
  };

  fetchReportFile = async () => {
    this.setState(prev => ({
      ...prev,
      ragDownload: {
        ...prev.ragDownload,
        isLoading: true,
      },
    }));

    let URL = `${API_ENDPOINTS.DOWNLOAD_RAG_REPORT}?`;
    const query = new URLSearchParams(window.location.search);

    if (query.get('project')) {
      URL += `project=${query.get('project')}&`;
    }
    if (this.state.userFilter.user) {
      URL += `user=${this.state.userFilter.id}&`;
    }

    const startDate =
      query.get('start') ||
      (this.state.dateFilter.startDate &&
      moment(this.state.dateFilter.startDate).isValid()
        ? moment(this.state.dateFilter.startDate).toISOString()
        : null);
    const endDate =
      query.get('end') ||
      (this.state.dateFilter.endDate &&
      moment(this.state.dateFilter.endDate).isValid()
        ? moment(this.state.dateFilter.endDate).toISOString()
        : null);

    if (startDate) {
      URL += `start=${startDate}&`;
    }
    if (endDate) {
      URL += `end=${endDate}`;
    }

    this.fetchData(URL).then(async data => {
      const sortedRag = this.sortRag(data.ragReport);
      const ragWorkBook = await this.createTrackerWorkbook(sortedRag);
      const sprintMetricsWorkBook = await this.createSprintMetricsReport(
        data.sprintMetricsReport,
      );
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ragWorkBook, 'Rag');
      XLSX.utils.book_append_sheet(wb, sprintMetricsWorkBook, 'Sprint Metrics');
      XLSX.writeFile(wb, 'rag_report.xlsx');
      this.setState(prev => ({
        ...prev,
        ragDownload: {
          ...prev.ragDownload,
          isLoading: false,
        },
      }));
    });
  };

  fetchUserList(shouldFetchData = false) {
    this.setState(prev => ({
      ...prev,
      userList: {
        ...prev.userList,
        isLoading: true,
      },
    }));

    // Cancel any pending User List API request
    if (this.currentUserListController) {
      this.currentUserListController.abort();
    }

    // Create new AbortController for this request
    this.currentUserListController = new AbortController();

    // Generate unique request ID to ignore stale responses
    this.currentUserListRequestId += 1;
    const requestId = this.currentUserListRequestId;

    this.fetchData(
      `${API_ENDPOINTS.USER_LIST}?limit=${USER_LIST_LIMIT}`,
      this.currentUserListController.signal,
    )
      .then(data => {
        // Only process response if it's from the latest request
        if (requestId !== this.currentUserListRequestId) {
          return;
        }

        const userLabelList = [];
        const userLabelMapping = {};
        get(data, 'docs', []).forEach(user => {
          user.label.forEach(label => {
            userLabelMapping[label] = user._id;
            userLabelList.push({
              value: JSON.stringify({
                id: user._id,
                label,
              }),
              label,
            });
          });
        });
        this.setState(
          prev => ({
            ...prev,
            userList: {
              data: userLabelList,
              isLoading: false,
              userLabelMapping,
            },
          }),
          () => {
            // Only fetch RAG data if explicitly requested
            if (shouldFetchData) {
              this.fetchRagData();
              this.fetchSprintMetricsData();
            }
          },
        );

        // Clear the controller after successful completion
        this.currentUserListController = null;
      })
      .catch(error => {
        // Don't update state if the request was aborted
        if (error.name === 'AbortError') {
          return;
        }

        // Only update state if this is the latest request
        if (requestId === this.currentUserListRequestId) {
          this.setState(prev => ({
            ...prev,
            userList: {
              ...prev.userList,
              isLoading: false,
            },
          }));
        }

        // Clear the controller after error
        this.currentUserListController = null;
      });
  }

  onTabChange = tab => {
    this.setState(
      prev => ({ ...prev, tab }),
      () => {
        setDeepLinkURL({
          tab: this.state.tab,
          project: this.state.projectFilter.index,
          user: this.state.userFilter.user,
          start: this.state.dateFilter.startDate,
          end: this.state.dateFilter.endDate,
        });
      },
    );
  };

  async fetchData(url, signal = null) {
    const options = { method: 'GET' };
    if (signal) {
      options.signal = signal;
    }
    const res = await request(url, options);
    return get(res, 'data');
  }

  getRowSpan(params) {
    if (params.data.teamLength) {
      return params.data.teamLength;
    }
    return 1;
  }

  memberVarianceCell(params) {
    const spentHours =
      params.data && (params.data.spentHours || params.data.spentHours === 0);
    const plannedEfforts =
      params.data &&
      (params.data.plannedEfforts || params.data.plannedEfforts === 0);
    if (spentHours && plannedEfforts) {
      return (
        <Tooltip
          title={`Total Spent hours : ${(params.data.spentHours / 3600).toFixed(
            2,
          )}, Total Planned hours: ${(
            params.data.plannedEfforts / 3600
          ).toFixed(2)}`}
        >
          <span className="rag-element">
            {params.value
              ? params.value
              : (
                  ((params.data.spentHours - params.data.plannedEfforts) /
                    (params.data.plannedEfforts || 1)) *
                  100
                ).toFixed(2)}
          </span>
        </Tooltip>
      );
    }

    return (
      <Tooltip title="Total Spent hours : NA, Total Planned hours: NA">
        <span className="rag-element">
          {params.value || params.value === 0 ? params.value : 'NA'}
        </span>{' '}
      </Tooltip>
    );
  }

  getRowClassNameSprintMetric = (params, projectMap) => {
    if (projectMap[params.data.index]) {
      return 'even-row';
    }

    return 'odd-row';
  };

  getRowClassName = (params, projectMap) => {
    const { data, node, api } = params;
    if (!data) return '';

    let rowClass = projectMap[data.index] ? 'even-row' : 'odd-row';

    // Check if freeze is true
    if (data.freeze === true) {
      rowClass += ' frozen-row'; // Apply border for frozen rows

      const prevNode = api.getDisplayedRowAtIndex(node.rowIndex - 1);
      const nextNode = api.getDisplayedRowAtIndex(node.rowIndex + 1);

      const prevFrozen =
        prevNode &&
        prevNode.data.freeze === true &&
        prevNode.data.index === data.index;
      const nextFrozen =
        nextNode &&
        nextNode.data.freeze === true &&
        nextNode.data.index === data.index;

      // Remove border between consecutive frozen rows
      if (prevFrozen) rowClass += ' no-top-border';
      if (nextFrozen) rowClass += ' frozen-row-border';
    }

    return rowClass;
  };

  onProjectSelect(event) {
    // Get project details
    let projectId = '';
    let pmUser = '';
    if (event) {
      const projectObj = this.state.projectList.raw.find(
        project => project.projectName === event,
      );
      if (projectObj) {
        projectId = projectObj._id;
        pmUser = projectObj.pmUser;
      }
    }

    // Single setState call to avoid race conditions
    this.setState(
      prev => ({
        ...prev,
        projectFilter: {
          ...prev.projectFilter,
          index: event || '',
          id: projectId,
          pmUser,
        },
      }),
      () => {
        setDeepLinkURL({
          tab: this.state.tab,
          project: this.state.projectFilter.index,
          user: this.state.userFilter.user,
          start: this.state.dateFilter.startDate,
          end: this.state.dateFilter.endDate,
        });
        // Add small delay to ensure URL is updated before fetching data
        setTimeout(() => {
          this.fetchRagData();
          this.fetchSprintMetricsData();
        }, 100);
      },
    );
  }

  onUserSelect(event) {
    const user = event ? JSON.parse(event) : {};
    this.setState(
      {
        userFilter: {
          user: user.label || '',
          id: user.id || '',
        },
      },
      () => {
        setDeepLinkURL({
          tab: this.state.tab,
          project: this.state.projectFilter.index,
          user: this.state.userFilter.user,
          start: this.state.dateFilter.startDate,
          end: this.state.dateFilter.endDate,
        });
        // Add small delay to ensure URL is updated before fetching data
        setTimeout(() => {
          this.fetchRagData();
          this.fetchSprintMetricsData();
        }, 100);
      },
    );
  }

  triggerCronAPI = async () => {
    const projectId = this.state.projectFilter.id;
    const URL = `${API_ENDPOINTS.TRIGGER_RAG_CRON}?projectId=${projectId}`;
    this.setState({
      triggerLoading: true,
      isCronRunning: true,
    });
    request(URL, {
      method: 'GET',
    })
      .then(res => {
        if (res.status === 1) {
          this.fetchUserList(true); // Pass true to fetch data after cron job
        } else {
          notification.error({
            message: res.message,
          });
        }
      })
      .catch(error => {
        notification.error({
          message: error.message,
        });
        this.setState({ triggerLoading: false, isCronRunning: false });
      })
      .finally(() => {
        this.setState({
          triggerLoading: false,
          isCronRunning: false,
        });
      });
  };

  checkCronStatus = async () => {
    const URL = `${API_ENDPOINTS.CRON_STATUS}`;
    request(URL, {
      method: 'GET',
    })
      .then(res => {
        if (res.status === 1) {
          this.setState({
            isCronRunning: res.data.isRagCronRunning,
          });
        }
      })
      .catch(error => {
        notification.error({
          message: error.message,
        });
      });
  };

  getSyncJiraDisabled = () => {
    if (
      // ** disabled false for: 2 hardcoded users & users with level 5
      SUPER_ADMIN_LIST.includes(this.state.user.email) ||
      this.state.user.role === 5
    ) {
      return false;
    }
    return this.state.user._id !== this.state.projectFilter.pmUser;
  };

  handleIndividualRagFreeze = async ragDetails => {
    try {
      const response = await request(API_ENDPOINTS.FREEZE_RAG, {
        method: 'POST',
        body: { ragIds: [ragDetails._id] },
      });

      if (get(response, 'status') !== 1) {
        throw new Error(response.message);
      }

      const deepCopy = cloneDeep(this.state);
      const ragDetailToUpdateIndex = deepCopy.rag.rowData.findIndex(
        item => item._id === ragDetails._id,
      );

      deepCopy.rag.rowData[ragDetailToUpdateIndex].freeze = true;

      this.setState(deepCopy);
    } catch (error) {
      notification.error({ message: error.message });
    }
  };

  render() {
    const { modalVars, sprintMetrics, rag } = this.state;
    const { rowData } = rag;
    const projectMap = {};
    const sprintMetricsMap = {};

    if (rowData.length) {
      rowData.forEach(data => {
        if (!projectMap[data.index]) {
          projectMap[data.index] = data.index % 2 === 0;
        }
      });
    }

    if (sprintMetrics.rowData.length) {
      sprintMetrics.rowData.forEach(data => {
        if (!sprintMetricsMap[data.index]) {
          sprintMetricsMap[data.index] = data.index % 2 === 0;
        }
      });
    }

    return (
      <StyledRagDiv>
        <Helmet>
          <title>RAG Report</title>
          <meta name="description" content="Rag report" />
        </Helmet>
        <div className="ragReportHeader">
          <PageHeader title="RAG Report" className="site-page-header" />
        </div>
        <div className="filter-container">
          <div className="filters">
            <DatePicker.RangePicker
              onChange={this.dateRangeFilterHandler}
              defaultValue={
                this.state.dateFilter.startDate && this.state.dateFilter.endDate
                  ? [
                      moment(this.state.dateFilter.startDate),
                      moment(this.state.dateFilter.endDate),
                    ]
                  : []
              }
              format={GENERIC_MOMENT_DATE_FORMAT}
            />
            <Select
              style={{ width: '150px', marginLeft: '0.5rem' }}
              placeholder="Select Project"
              options={this.state.projectList.data}
              onChange={this.onProjectSelect}
              defaultValue={this.state.projectFilter.index || undefined}
              allowClear
              showSearch
            />
            <Select
              style={{ width: '150px', marginLeft: '0.5rem' }}
              placeholder="Select Employee"
              options={this.state.userList.data}
              onChange={this.onUserSelect}
              defaultValue={this.state.userFilter.user || undefined}
              allowClear
              showSearch
            />
            {this.state.projectFilter.id && (
              <Button
                style={{ marginLeft: '0.5rem' }}
                onClick={this.triggerCronAPI}
                loading={this.state.triggerLoading}
                className="trigger-btn"
                data-testid="trigger-btn"
                disabled={
                  this.state.isCronRunning ||
                  this.getSyncJiraDisabled() ||
                  this.state.isSyncJiraDisabled
                }
              >
                Sync Jira
              </Button>
            )}
          </div>
          <div className="legends">
            <Legends />
          </div>
        </div>
        {this.state.isCronRunning ? (
          <div
            className="cron-inprogress"
            data-testid="cron-inprogress-message"
          >
            <span>
              <InfoCircleOutlined />
              &nbsp;
              {messages.CRON_ALREADY_RUNNING}
            </span>
          </div>
        ) : null}
        <Tabs
          defaultActiveKey={this.state.tab}
          destroyInactiveTabPane
          activeKey={this.state.tab}
          onChange={this.onTabChange}
          tabBarExtraContent={
            <div className="tab-extra-content">
              <Button
                className="download-btn"
                data-testid="download-btn"
                onClick={this.fetchReportFile}
                loading={this.state.ragDownload.isLoading}
              >
                Download Report
              </Button>
              {(this.state.user.role === 5 || this.state.user.role === 4) && (
                <Button
                  className="download-btn"
                  data-testid="download-btn"
                  onClick={this.freezeRag}
                >
                  Freeze{this.state.selectedIds.length ? ' Selected ' : ' '}RAGS
                </Button>
              )}
            </div>
          }
        >
          <Tabs.TabPane key="rag" tab={<div>RAG</div>}>
            <>
              {rag.isLoading ? (
                <Skeleton active />
              ) : (
                <div className="ag-theme-quartz" style={{ height: '600px' }}>
                  <div />
                  <AgGridReact
                    ref={this.ragTableRef}
                    columnDefs={rag.columnDefs}
                    rowData={rag.rowData}
                    rowSelection="multiple"
                    defaultColDef={{
                      suppressHeaderMenuButton: false,
                      floatingFilterComponentParams: {},
                    }}
                    suppressDragLeaveHidesColumns
                    suppressRowTransform
                    loadingCellRenderer={rag.isLoading}
                    getRowClass={params =>
                      this.getRowClassName(params, projectMap)
                    }
                    onRowDataUpdated={() => {
                      if (this.rowIndexToScrollTo !== null) {
                        this.ragTableRef.current.api.ensureIndexVisible(
                          this.rowIndexToScrollTo,
                          'top',
                        );
                        const rowNode = this.ragTableRef.current.api.getDisplayedRowAtIndex(
                          this.rowIndexToScrollTo,
                        );
                        if (rowNode) {
                          this.ragTableRef.current.api.flashCells({
                            rowNodes: [rowNode],
                          });
                          rowNode.setSelected(true);
                        }
                        this.rowIndexToScrollTo = null;
                      }
                    }}
                  />
                </div>
              )}
            </>
          </Tabs.TabPane>
          <Tabs.TabPane
            key="sprintMetrics"
            tab={<div>Sprint Metrics Report</div>}
          >
            {sprintMetrics.isLoading ? (
              <Skeleton active />
            ) : (
              <div className="ag-theme-quartz" style={{ height: '600px' }}>
                <div />
                <AgGridReact
                  ref={this.sprintMetricsTableRef}
                  headerHeight={100}
                  columnDefs={sprintMetrics.columnDefs}
                  suppressGroupRowsSticky
                  groupAllowUnbalanced
                  rowData={sprintMetrics.rowData}
                  defaultColDef={{
                    suppressHeaderMenuButton: false,
                    floatingFilterComponentParams: {},
                    sortable: true,
                    resizable: true,
                  }}
                  suppressDragLeaveHidesColumns
                  suppressRowTransform
                  loadingCellRenderer={sprintMetrics.isLoading}
                  getRowClass={params =>
                    this.getRowClassNameSprintMetric(params, sprintMetricsMap)
                  }
                  onRowDataUpdated={() => {
                    if (this.rowIndexToScrollTo !== null) {
                      this.sprintMetricsTableRef.current.api.ensureIndexVisible(
                        this.rowIndexToScrollTo,
                        'top',
                      );
                      const rowNode = this.sprintMetricsTableRef.current.api.getDisplayedRowAtIndex(
                        this.rowIndexToScrollTo,
                      );
                      if (rowNode) {
                        this.sprintMetricsTableRef.current.api.flashCells({
                          rowNodes: [rowNode],
                        });
                        rowNode.setSelected(true);
                      }
                      this.rowIndexToScrollTo = null;
                    }
                  }}
                />
              </div>
            )}
          </Tabs.TabPane>
        </Tabs>
        <CustomEditableModal
          title={modalVars.modalTitle}
          isVisible={modalVars.modalVisible}
          isEdit={modalVars.modalIsEdit}
          data={modalVars.modalContent}
          param={modalVars.modalField}
          id={modalVars.modalContentId}
          changeRowData={this.changeRowData}
          onClose={this.closeModal}
        />
      </StyledRagDiv>
    );
  }
}
