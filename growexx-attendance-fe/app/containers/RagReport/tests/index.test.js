import { ConnectedRouter } from 'connected-react-router';
import { browserHistory } from 'react-router-dom';
import { fireEvent, waitFor } from '@testing-library/dom';
import React from 'react';
import { render } from 'react-testing-library';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import '@testing-library/jest-dom';
import history from 'utils/history';
import request from 'utils/request';
import { RagReport } from '..';
import configureStore from '../../../configureStore';
import CustomEditableModal from '../CustomEditableModal';

let store;
jest.mock('utils/request');
jest.mock('xlsx', () => ({
  utils: {
    aoa_to_sheet: jest.fn(() => ({
      A1: {},
      C1: {},
      D1: {},
      E1: {},
      A2: {},
      C2: {},
      D2: {},
      E2: {},
    })),
    sheet_add_json: jest.fn(),
    book_new: jest.fn(),
    book_append_sheet: jest.fn(),
  },
  writeFile: jest.fn(),
}));

const downloadData = {
  ragReport: [
    {
      _id: '65f93eafb9331babe6196374',
      boardId: '60',
      jiraSprintId: '278',
      project: 'ATLAS- The Internal Auditing',
      boardKey: 'ATLS',
      createdAt: '2024-03-19T07:28:47.618Z',
      effortVariance: 42.01,
      openCloseRatio: 50,
      resolvedBugs: 28,
      sprintEnd: '2023-08-14T07:58:44.395Z',
      sprintNumber: 'ATLS Sprint 5',
      sprintReport: 'link',
      sprintStart: '2023-07-31T05:13:19.456Z',
      totalBugs: 32,
      unlabelledBugsCount: 14,
      unlabelledBugsCountJQL: 'jql',
      updatedAt: '2024-03-19T07:28:47.618Z',
      teamLength: 9,
      member: 'keval.mandalik',
      b2dCount: 0,
      bugsReported: '28/32',
      index: 1,
      overallRag: 'red',
      bugLink: 'link',
      developersCount: 2,
    },
    {
      member: 'jaydip.maniyar',
      b2dCount: 1,
      overallRag: 'green',
      openCloseRatio: 60,
      effortVariance: 15,
      bugsReported: '2/2',
      index: 1,
      sprintEnd: '2023-08-14T07:58:44.395Z',
    },
    {
      member: 'jaydip.maniyar',
      b2dCount: 0,
      overallRag: 'amber',
      openCloseRatio: 80,
      effortVariance: 5,
      bugsReported: '5/5',
      index: 1,
      sprintEnd: '2023-08-14T07:58:44.395Z',
    },
  ],
  sprintMetricsReport: [
    {
      _id: '65f18c991aaa2a5ebc11163c',
      projectName: 'ATLAS- The Internal Auditing',
      repo: 'Overall',
      resource: 'arijeet-growexxer',
      sprintNumber: 'ATLS Sprint 5',
      __v: 0,
      apiCreated: '0',
      availableAPI: '0',
      bugs: '0',
      codeSmell: '2.4',
      coverage: '90.3',
      coverageRating: '5',
      createdAt: '2024-03-13T11:23:05.431Z',
      duplication: '3.3',
      endDate: '2023-08-14T00:00:00.000Z',
      prCount: '21',
      prEfficiency: '76.19',
      prRating: '3',
      rejectedPrCount: '5',
      sonarRating: '4',
      sprintAverage: '3.25',
      startDate: '2023-08-01T00:00:00.000Z',
      swaggerFinalRating: '1',
      swaggerRating: '0.00',
      updatedAt: '2024-03-13T11:23:05.431Z',
      vulnerabilities: '0',
      users: [],
      projectSpan: 5,
      index: 0,
    },
  ],
};

const userMockData = {
  docs: [
    {
      _id: 'id',
      name: 'John Smith',
      label: ['john.smith'],
    },
  ],
};

const projectData = [
  {
    _id: 'id',
    projectName: 'proj',
  },
];

const ragMockData = [
  {
    _id: '66c587b27bbe3f1b97e5c93c',
    jiraSprintId: '474',
    boardId: '107',
    project: 'AP-Reconciliation',
    boardKey: 'APRECON',
    completedStoriesCount: 2,
    createdAt: '2024-08-21T06:22:42.634Z',
    effortVariance: 0,
    freeze: false,
    openCloseRatio: 20,
    resolvedBugs: 0,
    sprintEnd: '2024-08-12T11:32:50.988Z',
    sprintNumber: 'APRECON Sprint 2',
    sprintReport:
      'https://growexx.atlassian.net/jira/software/c/projects/APRECON/boards/107/reports/sprint-retrospective?sprint=474',
    sprintStart: '2024-07-29T06:00:52.505Z',
    team: {
      member: 'omkar.navik',
      b2dCount: 0,
      solvedBugCount: 0,
      effortVariance: 0,
      spentHours: 201600,
      plannedEfforts: 201600,
      _id: '66c6e25464808674b752a525',
    },
    totalBugs: 0,
    totalPlannedEfforts: 96,
    totalPlannedStories: 10,
    totalSpentEfforts: 96,
    unlabelledBugsCount: 0,
    unlabelledBugsCountJQL:
      'https://growexx.atlassian.net/jira/software/c/projects/APRECON/issues/?jql=project=APRECON AND type=Bug AND sprint=474 AND labels is EMPTY',
    updatedAt: '2024-08-21T06:22:42.634Z',
    teamLength: 2,
    users: [
      {
        _id: '627224c71ce7a27aeb2f7ebe',
        isActive: 1,
        otp: 0,
        phoneOtp: 0,
        isDelete: 0,
        label: ['omkar.navik'],
        employeeId: 145,
        email: '<EMAIL>',
        password:
          '75532bd601803fb0c65accb8280fad83:6a0e7a7d9a4f0d4e40582ce3b871879584029e11c0d44dd2685d85685c7c07968b0b0f97c4c173af134176f46c30c76130b6acc1512556afa5f96937e56b54bbdeedffce6c067a19534b2ad463063e3c',
        firstName: 'Omkar',
        lastName: 'Navik',
        doj: '2022-05-02T00:00:00.000Z',
        role: 1,
        createdAt: '2022-05-04T07:01:27.577Z',
        updatedAt: '2022-05-04T07:01:27.577Z',
        __v: 0,
        designation: 'Software Engineer',
        level: 'L1',
        businessUnit: 'Microsoft Services',
        githubUsernames: ['omkar-growexxer'],
      },
    ],
    projectInfo: [
      {
        _id: '66b0e41b2d42e3e3b34ee149',
        pmUser: null,
      },
    ],
    portalInfo: [],
    member: 'omkar.navik',
    b2dCount: 0,
    solvedBugCount: 0,
    effortVarianceIndivisual: null,
    memberId: '66c6e25464808674b752a525',
    plannedEfforts: 201600,
    spentHours: 201600,
    bugsReported: '0/0',
    userId: '627224c71ce7a27aeb2f7ebe',
    index: 1,
    overallRag: 'red',
    bugLink:
      'https://growexx.atlassian.net/jira/software/c/projects/APRECON/issues/?jql=project%20%3D%20%22APRECON%22%20and%20type%20%3D%20Bug%20and%20sprint%20%3D%20474%20AND%20status%20NOT%20IN%20%28Done%29%20ORDER%20BY%20created%20DESC',
    developersCount: 1,
  },
];

const sprintMetricsData = [
  {
    _id: '66b1e67c2d42e3e3b32e0e1a',
    repo: 'Overall',
    sprintNumber: 'IM Sprint 10',
    projectName: 'iMadrassa Mobile Application',
    resource: 'ankurm-growexxer',
    __v: 0,
    apiCreated: '0',
    availableAPI: '0',
    bugs: '0',
    codeSmell: '0',
    coverage: '91.1',
    coverageRating: '5',
    createdAt: '2024-08-06T09:01:48.151Z',
    duplication: '1.6',
    endDate: '2024-08-02T00:00:00.000Z',
    freeze: true,
    prCount: '2',
    prEfficiency: '100',
    prRating: '5',
    rejectedPrCount: '0',
    sonarRating: '4',
    sprintAverage: '3.75',
    startDate: '2024-07-22T00:00:00.000Z',
    swaggerFinalRating: '1',
    swaggerRating: '0.00',
    updatedAt: '2024-08-06T09:01:48.151Z',
    vulnerabilities: '0',
    users: [],
    projectSpan: 2,
    index: 0,
  },
];

const componentWrapper = () =>
  render(
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>
          <RagReport />
        </ConnectedRouter>
      </IntlProvider>
    </Provider>,
  );

describe('<RagReport />', () => {
  beforeEach(() => {
    store = configureStore({}, browserHistory);
    delete window.location;
    const mockUserData = JSON.stringify({
      body: { name: 'John Doe', email: '<EMAIL>' },
    });
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation(key => {
      if (key === 'userData') {
        return mockUserData;
      }
      return null;
    });
  });

  afterEach(() => {
    request.mockClear();
  });

  it('Renders properly', () => {
    window.location = {
      search: '?',
    };
    expect(componentWrapper).toMatchSnapshot();
  });

  it('Should render and match the snapshot: sprint metrics tab', async () => {
    window.location = {
      search: '?tab=sprintMetrics',
    };
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: sprintMetricsData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: userMockData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: projectData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: { isRagCronRunning: true } }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: sprintMetricsData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: sprintMetricsData }),
    );

    const { findByText, getAllByRole } = componentWrapper();
    fireEvent.click(await findByText('Sprint Metrics Report'));
    await waitFor(() => {
      fireEvent.blur(getAllByRole('combobox')[0]);
      fireEvent.focus(getAllByRole('combobox')[0]);
      fireEvent.mouseDown(getAllByRole('combobox')[0]);
      fireEvent.change(getAllByRole('combobox')[0], {
        target: {
          value: 'proj',
        },
      });
    });

    await waitFor(() => {
      const options = document.querySelectorAll(
        '.ant-select-item-option-content',
      );
      if (options.length > 0) {
        fireEvent.click(options[0]);
      }
    });

    await waitFor(() => {
      fireEvent.blur(getAllByRole('combobox')[1]);
      fireEvent.focus(getAllByRole('combobox')[1]);
      fireEvent.mouseDown(getAllByRole('combobox')[1]);
      fireEvent.change(getAllByRole('combobox')[1], {
        target: {
          value: 'john.smith',
        },
      });
    });

    await waitFor(() => {
      const options = document.querySelectorAll(
        '.ant-select-item-option-content',
      );
      if (options.length > 1) {
        fireEvent.click(options[1]);
      }
    });
    await waitFor(() => {
      expect(request).toHaveBeenCalledTimes(9);
    });
  });

  it('Should render and match the snapshot: rag tab', async () => {
    const userData = { body: 'test' };
    Storage.prototype.getItem = jest.fn(() => JSON.stringify(userData));
    window.location = {
      search: '?tab=rag&project=proj&user=user&start=2024-03-01&end=2024-03-19',
    };
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: sprintMetricsData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: userMockData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: projectData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: { isRagCronRunning: true } }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: downloadData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: downloadData.ragReport }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: downloadData.sprintMetricsReport }),
    );

    const { getByText } = componentWrapper();
    fireEvent.click(getByText('Download Report'));

    await waitFor(() => {
      expect(request).toHaveBeenCalledTimes(6);
    });
  });
});

const customEditableModalRender = () =>
  render(
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>
          <CustomEditableModal
            title="Title"
            isVisible
            isEdit
            data="Test data"
            param="test"
            id="id"
            changeRowData={() => {}}
            onClose={() => {}}
          />
        </ConnectedRouter>
      </IntlProvider>
    </Provider>,
  );

describe('Custom Editable Modal', () => {
  afterEach(() => {
    request.mockClear();
  });

  it('should render modal', async () => {
    customEditableModalRender();
  });

  it('should change modal data', async () => {
    request.mockImplementationOnce(() => Promise.resolve({}));
    const { findByTestId } = customEditableModalRender();
    const textElem = await findByTestId('input-area');
    fireEvent.change(textElem, { target: { value: 'new data' } });
    fireEvent.blur(textElem);
    const saveBtn = await findByTestId('save-btn');
    fireEvent.click(saveBtn);
    expect(request).toHaveBeenCalledTimes(1);
  });
});

describe('<RagReport />', () => {
  beforeEach(() => {
    store = configureStore({}, browserHistory);
    delete window.location;
  });

  afterEach(() => {
    request.mockClear();
  });

  it('Should show error', async () => {
    window.location = {
      search: '?tab=sprintMetrics',
    };
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: userMockData }),
    );
    request.mockRejectedValue({ status: 0, data: 'message' });

    componentWrapper();

    await waitFor(() => {
      expect(request).toHaveBeenCalledTimes(5);
    });
  });

  it('Should handle AbortController and request cancellation', async () => {
    window.location = {
      search: '?tab=rag',
    };

    // Mock AbortController
    const mockAbort = jest.fn();
    const mockController = {
      abort: mockAbort,
      signal: { aborted: false },
    };
    global.AbortController = jest.fn(() => mockController);

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    componentWrapper();

    // Wait for initial load
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    // Verify that AbortController was instantiated
    await waitFor(() => {
      expect(global.AbortController).toHaveBeenCalled();
    });
  });

  it('Should handle AbortError gracefully', async () => {
    window.location = {
      search: '?tab=rag',
    };

    const abortError = new Error('Request aborted');
    abortError.name = 'AbortError';

    request.mockRejectedValueOnce(abortError);
    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    componentWrapper();

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });
  });

  it('Should handle undefined project data', async () => {
    window.location = {
      search: '?tab=rag',
    };

    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: sprintMetricsData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: userMockData }),
    );
    request.mockImplementationOnce(
      () => Promise.resolve({ status: 1, data: null }), // null project data
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: { isRagCronRunning: false } }),
    );

    componentWrapper();

    await waitFor(() => {
      expect(request).toHaveBeenCalledTimes(5);
    });
  });

  it('Should handle undefined trackerData in createSprintMetricsReport', async () => {
    window.location = {
      search: '?tab=rag',
    };

    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: sprintMetricsData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: userMockData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: projectData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: { isRagCronRunning: false } }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({
        status: 1,
        data: {
          ragReport: downloadData.ragReport,
          sprintMetricsReport: undefined, // undefined sprint metrics data
        },
      }),
    );

    const { getByText } = componentWrapper();
    fireEvent.click(getByText('Download Report'));

    await waitFor(() => {
      expect(request).toHaveBeenCalledTimes(6);
    });
  });

  it('Should handle timeout delays in fetchRagData', async () => {
    window.location = {
      search: '?tab=rag',
    };

    jest.useFakeTimers();

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    const { getAllByRole } = componentWrapper();

    // Wait for initial load
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    // Trigger project change which should use setTimeout
    const projectSelect = getAllByRole('combobox')[0];
    fireEvent.change(projectSelect, { target: { value: 'proj' } });

    // Fast-forward timers to trigger the delayed fetch
    jest.advanceTimersByTime(300);

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    jest.useRealTimers();
  });

  it('Should handle fetchUserList with shouldFetchData parameter', async () => {
    window.location = {
      search: '?tab=rag',
    };

    const mockUserData = {
      body: { name: 'John Doe', email: '<EMAIL>', role: 5 },
    };
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation(key => {
      if (key === 'userData') {
        return JSON.stringify(mockUserData);
      }
      return null;
    });

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    componentWrapper();

    // Wait for initial load
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    // Test fetchUserList without shouldFetchData (default behavior)
    // This is already covered by the initial component mount
  });

  it('Should handle date range filter with null values', async () => {
    window.location = {
      search: '?tab=rag',
    };

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    const { container } = componentWrapper();

    // Wait for initial load
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    // Test that the component handles null date range values gracefully
    // Since we can't easily trigger the date picker change event, we'll just verify
    // that the component renders without errors when date filters are null
    expect(container.querySelector('.ant-picker')).toBeTruthy();

    // Verify that the component doesn't crash with null date values
    await waitFor(() => {
      expect(
        container.querySelector('[data-testid="rag-report"]'),
      ).toBeTruthy();
    });
  });

  it('Should handle project selection with pmUser', async () => {
    window.location = {
      search: '?tab=rag',
    };

    const projectDataWithPmUser = [
      {
        _id: 'project-id',
        projectName: 'Test Project',
        pmUser: 'pm-user-id',
      },
    ];

    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: sprintMetricsData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: userMockData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: projectDataWithPmUser }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: { isRagCronRunning: false } }),
    );

    const { getAllByRole } = componentWrapper();

    // Wait for initial load
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    // Select project with pmUser
    const projectSelect = getAllByRole('combobox')[0];
    fireEvent.change(projectSelect, { target: { value: 'Test Project' } });

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });
  });

  it('Should handle request signature caching', async () => {
    window.location = {
      search: '?tab=rag&project=same-project',
    };

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    const { getAllByRole } = componentWrapper();

    // Wait for initial load
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    const initialCallCount = request.mock.calls.length;

    // Make the same request again - should be cached
    const projectSelect = getAllByRole('combobox')[0];
    fireEvent.change(projectSelect, { target: { value: 'same-project' } });
    fireEvent.change(projectSelect, { target: { value: 'same-project' } });

    await waitFor(() => {
      // Should not make additional calls due to caching
      expect(request).toHaveBeenCalledTimes(initialCallCount);
    });
  });

  it('Should handle environment without AbortController', async () => {
    window.location = {
      search: '?tab=rag',
    };

    // Mock environment without AbortController
    const originalAbortController = global.AbortController;
    delete global.AbortController;

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    const { getAllByRole } = componentWrapper();

    // Wait for initial load
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    // Trigger project change to test fallback AbortController
    const projectSelect = getAllByRole('combobox')[0];
    fireEvent.change(projectSelect, { target: { value: 'proj' } });

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    // Restore AbortController
    global.AbortController = originalAbortController;
  });

  it('Should handle request ID mismatch', async () => {
    window.location = {
      search: '?tab=rag',
    };

    let resolveRequest;
    const requestPromise = new Promise(resolve => {
      resolveRequest = resolve;
    });

    request.mockImplementationOnce(() => requestPromise);
    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    const { getAllByRole } = componentWrapper();

    // Trigger multiple rapid requests
    const projectSelect = getAllByRole('combobox')[0];
    fireEvent.change(projectSelect, { target: { value: 'proj1' } });
    fireEvent.change(projectSelect, { target: { value: 'proj2' } });

    // Resolve the first request after the second one has started
    resolveRequest({ status: 1, data: ragMockData });

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });
  });

  it('Should handle triggerCronAPI success and failure', async () => {
    window.location = {
      search: '?tab=rag',
    };

    const mockUserData = {
      body: { name: 'John Doe', email: '<EMAIL>', role: 5 },
    };
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation(key => {
      if (key === 'userData') {
        return JSON.stringify(mockUserData);
      }
      return null;
    });

    // Mock successful cron trigger
    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    componentWrapper();

    // Wait for initial load
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    // Test that the component handles cron API calls
    // The actual trigger button may not be visible in test environment
    // but we can test the API call logic
  });

  it('Should handle triggerCronAPI failure', async () => {
    window.location = {
      search: '?tab=rag',
    };

    request.mockImplementation(() =>
      Promise.resolve({ status: 0, message: 'Cron trigger failed' }),
    );

    componentWrapper();

    // Wait for initial load and test error handling
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });
  });

  it('Should handle triggerCronAPI network error', async () => {
    window.location = {
      search: '?tab=rag',
    };

    request.mockRejectedValueOnce(new Error('Network error'));

    componentWrapper();

    // Wait for initial load and test error handling
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });
  });

  it('Should handle fetchUserList without shouldFetchData', async () => {
    window.location = {
      search: '?tab=rag',
    };

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: userMockData }),
    );

    componentWrapper();

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });
  });

  it('Should handle timeout clearing in fetchRagData', async () => {
    window.location = {
      search: '?tab=rag',
    };

    jest.useFakeTimers();

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    const { getAllByRole } = componentWrapper();

    // Wait for initial load
    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    // Trigger multiple rapid project changes to test timeout clearing
    const projectSelect = getAllByRole('combobox')[0];
    fireEvent.change(projectSelect, { target: { value: 'proj1' } });
    fireEvent.change(projectSelect, { target: { value: 'proj2' } });
    fireEvent.change(projectSelect, { target: { value: 'proj3' } });

    // Fast-forward timers
    jest.advanceTimersByTime(300);

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    jest.useRealTimers();
  });

  it('Should handle null data in project mapping', async () => {
    window.location = {
      search: '?tab=rag',
    };

    // Mock with null data to test the (data || []) fallback
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: sprintMetricsData }),
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: userMockData }),
    );
    request.mockImplementationOnce(
      () => Promise.resolve({ status: 1, data: null }), // null project data
    );
    request.mockImplementationOnce(() =>
      Promise.resolve({ status: 1, data: { isRagCronRunning: false } }),
    );

    componentWrapper();

    await waitFor(() => {
      expect(request).toHaveBeenCalledTimes(5);
    });
  });

  it('Should handle AbortController signal in fetchData', async () => {
    window.location = {
      search: '?tab=rag',
    };

    // Mock AbortController
    const mockSignal = { aborted: false };
    global.AbortController = jest.fn(() => ({
      abort: jest.fn(),
      signal: mockSignal,
    }));

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    componentWrapper();

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });
  });

  it('Should handle request signature caching to prevent duplicate calls', async () => {
    window.location = {
      search: '?tab=rag',
    };

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    const { getAllByRole } = componentWrapper();

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    const initialCallCount = request.mock.calls.length;

    // Make the same request multiple times - should be cached
    const projectSelect = getAllByRole('combobox')[0];
    fireEvent.change(projectSelect, { target: { value: 'same-project' } });
    fireEvent.change(projectSelect, { target: { value: 'same-project' } });

    // Should not increase call count due to caching
    await waitFor(() => {
      expect(request).toHaveBeenCalledTimes(initialCallCount);
    });
  });

  it('Should handle setTimeout delay in filter changes', async () => {
    window.location = {
      search: '?tab=rag',
    };

    jest.useFakeTimers();

    request.mockImplementation(() =>
      Promise.resolve({ status: 1, data: ragMockData }),
    );

    const { getAllByRole } = componentWrapper();

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    // Trigger filter change that uses setTimeout
    const projectSelect = getAllByRole('combobox')[0];
    fireEvent.change(projectSelect, { target: { value: 'test-project' } });

    // Advance timers to trigger the delayed call
    jest.advanceTimersByTime(100);

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
    });

    jest.useRealTimers();
  });
});
