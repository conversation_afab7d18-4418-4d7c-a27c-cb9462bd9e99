import React from 'react';
import { act, fireEvent, render, wait } from 'react-testing-library';
import { Provider } from 'react-redux';
import { IntlProvider } from 'react-intl';
import { ConnectedRouter } from 'connected-react-router/immutable';
import userEvent from '@testing-library/user-event';
import { createMemoryHistory } from 'history';
import request from 'utils/request';
import CustomInput from '../CustomInput';
import configureStore from '../../../configureStore';

jest.mock('utils/request');

const selectDropdown = async (
  selectorIndex,
  optionIndex = 0,
  componentWrapper,
) => {
  await act(async () => {
    const { getAllByRole } = componentWrapper();

    // Click on the dropdown
    const select = getAllByRole('combobox')[selectorIndex];
    await wait(async () => {
      userEvent.click(select, undefined);
    });

    // Find dropdown option
    const option = document.getElementsByClassName(
      'ant-select-item-option-content',
    )[optionIndex];
    // Select option
    await wait(() => userEvent.click(option, undefined));
    fireEvent.blur(select);
  });
};

const history = createMemoryHistory();
const store = configureStore({}, history);
const componentWrapper = stubProps =>
  render(
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>
          <CustomInput {...stubProps} />
        </ConnectedRouter>
      </IntlProvider>
    </Provider>,
  );

describe('<CustomInput />', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  test('It should render icon as a snapshot', () => {
    const {
      container: { firstChild },
    } = render(<CustomInput isDisabled />);
    expect(firstChild).toMatchSnapshot();
  });

  test('It should render span if disabled and data is passed', () => {
    const { getByTestId } = render(<CustomInput data={1} isDisabled />);
    const btn = getByTestId('info-data');
    expect(btn).toBeTruthy();
  });

  test('It should render select if param is client escalation', () => {
    const { getByTestId } = render(
      <CustomInput data={1} isDisabled={false} param="clientEscalations" />,
    );
    const btn = getByTestId('client-escalations');
    expect(btn).toBeTruthy();
  });

  test('It should change client escalation', async () => {
    request.mockImplementationOnce(() => Promise.resolve({}));
    const { getByTestId } = componentWrapper({
      data: 2,
      isDisabled: false,
      param: 'clientEscalations',
      changeRowData: jest.fn(),
    });
    const btn = getByTestId('client-escalations');
    expect(btn).toBeTruthy();
    await selectDropdown(0, 2, componentWrapper);
    expect(request).toHaveBeenCalledTimes(0);
  });

  test('It should change tech or process audit score', async () => {
    request.mockImplementationOnce(() => Promise.resolve({}));
    const changeRowData = jest.fn();
    const { findByPlaceholderText } = componentWrapper({
      data: 1,
      isDisabled: false,
      param: 'techAudit',
      changeRowData,
    });
    const inputElem = await findByPlaceholderText('Enter Audit Score');
    fireEvent.change(inputElem, {
      target: { value: 2 },
    });
    fireEvent.blur(inputElem);
    expect(request).toHaveBeenCalledTimes(1);
  });

  test('It should allow clearing tech or process audit score when field is emptied', async () => {
    request.mockImplementationOnce(() => Promise.resolve({}));
    const changeRowData = jest.fn();
    const { findByPlaceholderText } = componentWrapper({
      data: 1,
      isDisabled: false,
      param: 'techAudit',
      changeRowData,
    });
    const inputElem = await findByPlaceholderText('Enter Audit Score');
    fireEvent.change(inputElem, {
      target: { value: '' },
    });
    fireEvent.blur(inputElem);
    expect(request).toHaveBeenCalledTimes(1);
  });
});
